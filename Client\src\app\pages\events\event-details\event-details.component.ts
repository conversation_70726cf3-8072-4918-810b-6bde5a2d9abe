import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { finalize } from 'rxjs/operators';
import { EventsService } from '../../../Core/Services/events.service';
import { AuthService } from '../../../Core/Services/auth.service';
import { DateUtilsService } from '../../../Core/Services/date-utils.service';
import { NotificationService } from '../../../Core/Services/notification.service';
import { Event, EventLocationType } from '../../../Core/Models/events';
import { environment } from '../../../../environments/environment';
import { HttpErrorResponse } from '@angular/common/http';
import { FormControl, Validators } from '@angular/forms';

@Component({
  selector: 'app-event-details',
  standalone: false,
  templateUrl: './event-details.component.html',
  styleUrl: './event-details.component.scss',
  providers: [MessageService],
})
export class EventDetailsComponent implements OnInit {
  event: Event | null = null;
  isLoading = false;
  error: string | null = null;

  private apiUrl = environment.apiUrl;

  // Make enum available to template
  EventLocationType = EventLocationType;

  // For event expiration check
  private eventExpirationTimer: any;

  // Rejection dialog properties
  showRejectDialog = false;
  rejectionReasonControl = new FormControl('', Validators.required);
  rejectionReasons = [
    {
      label: 'Safety And Security Concerns',
      value: 'Safety And Security Concerns',
    },
    { label: 'Conflicting Dates', value: 'Conflicting Dates' },
    { label: 'Incomplete Application', value: 'Incomplete Application' },
    { label: 'Lack Of Originality', value: 'Lack Of Originality' },
    { label: 'Inappropriate Content', value: 'Inappropriate Content' },
    { label: 'Venue Unavailable', value: 'Venue Unavailable' },
    { label: 'Insufficient Details', value: 'Insufficient Details' },
    { label: 'Other', value: 'Other' },
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private eventsService: EventsService,
    private messageService: MessageService,
    public authService: AuthService,
    private dateUtils: DateUtilsService,
    private notificationService: NotificationService,
  ) {}

  ngOnInit(): void {
    this.loadEventDetails();
  }

  ngOnDestroy(): void {
    // Clear the timer when component is destroyed
    if (this.eventExpirationTimer) {
      clearTimeout(this.eventExpirationTimer);
    }
  }

  // Utility method to get full image path
  getFullImagePath(relativePath: string): string {
    if (!relativePath) return 'assets/images/placeholder.jpg';

    // If it's already a full URL (from S3), return it as is
    if (relativePath.startsWith('http')) {
      return relativePath;
    }

    // Check if the path already contains /api/Files
    if (relativePath.includes('/api/Files/')) {
      // Path already has /api/Files/, just prepend the API URL
      return `${this.apiUrl}${relativePath}`;
    }

    // Otherwise, use the Files controller
    return `${this.apiUrl}/api/Files/${relativePath.replace(/^\/+/, '')}`;
  }

  loadEventDetails(): void {
    this.isLoading = true;
    this.error = null;

    const id = this.route.snapshot.paramMap.get('id');

    if (id) {
      const numericId = Number(id);

      this.eventsService
        .getEventById(numericId)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe({
          next: (event) => {
            this.event = event;

            if (this.event && this.event.location) {
              // Determine location type based on available data if not set
              if (
                !this.event.locationType ||
                this.event.locationType === undefined
              ) {
                // Check if it's an online event (has meeting ID and address fields are N/A)
                if (
                  this.event.location.meetingId &&
                  this.event.location.meetingId !== 'N/A' &&
                  this.event.location.meetingId.trim() !== '' &&
                  (this.event.location.address1 === 'N/A' ||
                    !this.event.location.address1)
                ) {
                  this.event.locationType = EventLocationType.Online;
                }
                // Check if it's a venue event (has address data and no meeting ID)
                else if (
                  this.event.location.address1 &&
                  this.event.location.address1 !== 'N/A' &&
                  this.event.location.address1.trim() !== '' &&
                  (!this.event.location.meetingId ||
                    this.event.location.meetingId === 'N/A')
                ) {
                  this.event.locationType = EventLocationType.Venue;
                }
                // Default to Venue if we can't determine
                else {
                  this.event.locationType = EventLocationType.Venue;
                }
              }
            }

            // Check if event is in "Submitted" status and needs auto-rejection
            if (this.event.statusName === 'Submitted') {
              // Check if event start time has passed
              if (this.hasEventStarted()) {
                this.autoRejectEvent();
              } else {
                // Setup timer for auto-rejection at event start time
                this.setupAutoRejectTimer();
              }
            }

            // Setup timer to hide approval buttons 30 minutes before event starts
            this.setupEventExpirationTimer();
          },
          error: (err) => {
            this.error = err.message;
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: err.message || 'Failed to load event details',
            });
          },
        });
    } else {
      this.error = 'Event ID not provided';
      this.isLoading = false;
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Event ID not provided',
      });
    }
  }

  // Check if the event has already started
  hasEventStarted(): boolean {
    if (!this.event || !this.event.eventStarts) return false;

    // Convert UTC event start time to IST before comparing
    const eventStartTime = this.dateUtils
      .convertUtcToIst(this.event.eventStarts)
      .getTime();
    const now = new Date().getTime();

    return now >= eventStartTime;
  }

  // Check if the event is within 30 minutes of starting
  isEventWithin30Minutes(): boolean {
    if (!this.event || !this.event.eventStarts) return false;

    // Convert UTC event start time to IST before comparing
    const eventStartTime = this.dateUtils
      .convertUtcToIst(this.event.eventStarts)
      .getTime();
    const thirtyMinutesBefore = eventStartTime - 30 * 60 * 1000; // 30 minutes in milliseconds
    const now = new Date().getTime();

    return now >= thirtyMinutesBefore;
  }

  // Auto-reject the event when start time is reached
  autoRejectEvent(): void {
    if (!this.event || !this.event.id) return;

    // Only auto-reject events in "Submitted" status
    if (this.event.statusName !== 'Submitted') return;

    this.isLoading = true;
    this.eventsService
      .rejectEvent(this.event.id, 'Event start time has passed')
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'info',
            summary: 'Event Auto-Rejected',
            detail:
              'This event has been automatically rejected as its start time has passed.',
          });

          // Refresh notifications to reflect status change
          this.notificationService.refreshNotifications();

          // Reload event details to show updated status
          this.loadEventDetails();
        },
        error: (err) => {
          console.error('Error auto-rejecting event:', err);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to auto-reject the event. Please try again later.',
          });
        },
      });
  }

  // Setup timer to auto-reject the event when start time is reached
  setupAutoRejectTimer(): void {
    if (!this.event || !this.event.eventStarts) return;

    // Convert UTC event start time to IST before comparing
    const eventStartTime = this.dateUtils
      .convertUtcToIst(this.event.eventStarts)
      .getTime();
    const now = new Date().getTime();

    // If the event has already started, reject it immediately
    if (now >= eventStartTime) {
      this.autoRejectEvent();
      return;
    }

    // Set timeout to auto-reject the event when it starts
    const timeUntilStart = eventStartTime - now;
    setTimeout(() => {
      this.autoRejectEvent();
    }, timeUntilStart);
  }

  setupEventExpirationTimer(): void {
    if (!this.event || !this.event.eventStarts) return;

    // Calculate time until 30 minutes before event starts
    // Convert UTC event start time to IST before comparing
    const eventStartTime = this.dateUtils
      .convertUtcToIst(this.event.eventStarts)
      .getTime();
    const expirationTime = eventStartTime - 30 * 60 * 1000; // 30 minutes in milliseconds
    const now = new Date().getTime();

    // If the event is already within 30 minutes of starting, hide approval buttons immediately
    if (now >= expirationTime) {
      // No need to show a message or navigate away, just update the UI
      return;
    }

    // Set timeout to hide the approval buttons when the event is within 30 minutes of starting
    const timeUntilExpiration = expirationTime - now;
    this.eventExpirationTimer = setTimeout(() => {
      this.messageService.add({
        severity: 'info',
        summary: 'Approval Window Closed',
        detail:
          'This event can no longer be approved or rejected as it starts in less than 30 minutes.',
      });

      // Force UI update by reloading the component
      this.loadEventDetails();
    }, timeUntilExpiration);
  }

  goBack(): void {
    this.router.navigate(['/events']);
  }

  editEvent(): void {
    if (this.event && this.event.id) {
      this.router.navigate(['/events', this.event.id, 'edit']);
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Cannot edit event: Event ID is missing',
      });
    }
  }

  canEdit(): boolean {
    if (!this.event) return false;

    // No one can edit events that have already started
    if (this.hasEventStarted()) return false;

    // All authenticated users can edit events that haven't started
    return true;
  }

  approveEvent(): void {
    if (!this.event || !this.event.id) return;

    this.isLoading = true;
    this.eventsService
      .approveEvent(this.event.id)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Event approved successfully',
          });

          // Refresh notifications to reflect status change
          this.notificationService.refreshNotifications();

          // Reload event details to show updated status
          this.loadEventDetails();
        },
        error: (err) => {
          console.error('Error approving event:', err);

          // Handle specific error cases
          if (err instanceof HttpErrorResponse && err.status === 403) {
            this.messageService.add({
              severity: 'error',
              summary: 'Permission Denied',
              detail: 'You do not have permission to approve events',
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: err.message || 'Failed to approve event',
            });
          }
        },
      });
  }

  rejectEvent(): void {
    if (!this.event || !this.event.id) return;

    // Reset form and show dialog
    this.rejectionReasonControl.reset();
    this.rejectionReasonControl.markAsUntouched();
    this.rejectionReasonControl.markAsPristine();

    // Use setTimeout to ensure proper dialog initialization
    setTimeout(() => {
      this.showRejectDialog = true;
    }, 0);
  }

  onRejectDialogCancel(): void {
    this.rejectionReasonControl.reset();
    this.rejectionReasonControl.markAsUntouched();
    this.rejectionReasonControl.markAsPristine();
    this.showRejectDialog = false;
  }

  onRejectDialogSubmit(): void {
    // Mark as touched to show validation errors if any
    this.rejectionReasonControl.markAsTouched();

    if (this.rejectionReasonControl.invalid || !this.event?.id) {
      return;
    }

    const reason = this.rejectionReasonControl.value as string;
    this.showRejectDialog = false;

    this.isLoading = true;
    this.eventsService
      .rejectEvent(this.event.id, reason)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Event rejected successfully',
          });

          // Reset form after successful submission
          this.rejectionReasonControl.reset();
          this.rejectionReasonControl.markAsUntouched();
          this.rejectionReasonControl.markAsPristine();

          // Refresh notifications to reflect status change
          this.notificationService.refreshNotifications();

          // Reload event details to show updated status
          this.loadEventDetails();
        },
        error: (err) => {
          console.error('Error rejecting event:', err);

          // Handle specific error cases
          if (err instanceof HttpErrorResponse && err.status === 403) {
            this.messageService.add({
              severity: 'error',
              summary: 'Permission Denied',
              detail: 'You do not have permission to reject events',
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: err.message || 'Failed to reject event',
            });
          }
        },
      });
  }
  formatEventType(type: string): string {
    switch (type) {
      case 'AppearanceOrSigning':
        return 'Appearance Or Signing';
      case 'Attraction':
        return 'Attraction';
      case 'CampTripOrRetreat':
        return 'CampTrip Or Retreat';
      case 'ClassTrainingOrWorkshop':
        return 'Class And Training Or Workshop';
      case 'ConcertOrPerformance':
        return 'Concert Or Performance';
      case 'Conference':
        return 'Conference';
      case 'Convention':
        return 'Convention';
      case 'DinnerOrGala':
        return 'Dinner Or Gala';
      case 'FestivalOrFair':
        return 'Festival Or Fair';
      case 'GamesOrCompetition':
        return 'Games Or Competition';
      case 'MeetingOrNetworkingEvent':
        return 'Meeting Or Networking Event';
      case 'Other':
        return 'Other';
      case 'PartyOrSocialGathering':
        return 'Party Or Social Gathering';
      case 'Rally':
        return 'Rally';
      case 'Screening':
        return 'Screening';
      case 'SeminarOrTalk':
        return 'Seminar Or Talk';
      case 'Tour':
        return 'Tour';
      case 'Tournament':
        return 'Tournament';
      case 'TradeShowConsumerShowOrExpo':
        return 'Trade Show Consumer Show Or Expo';
      default:
        return type;
    }
  }

  formatEventCategory(category: string): string {
    switch (category) {
      case 'CareersAndEmployment':
        return 'Careers And Employment';
      case 'CommunityResources':
        return 'Community Resources';
      case 'EarlyChildhood':
        return 'Early Childhood';
      case 'HealthWellness':
        return 'Health And Wellness';
      case 'MaternalHealthCare':
        return 'Maternal Health Care';
      case 'RentalHousing':
        return 'Rental Housing';
      default:
        return category;
    }
  }
}
